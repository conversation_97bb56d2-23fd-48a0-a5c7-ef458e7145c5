import React, { createContext, useContext, useState, useEffect, Component, useCallback } from "react";
import { useAuth } from "../hooks/useAuth";
import useVideoCall from "../hooks/useVideoCall";
import VideoCallComponent from "../components/VideoCallComponent";
import VideoIncomingCallModal from "../components/VideoIncomingCallModal";
import useChat from "../hooks/useChat";

// Error boundary component to catch errors in video call components
class VideoCallErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console
    console.error("Video call component error:", error);
    console.error("Error details:", errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <div style={{
          padding: "20px",
          backgroundColor: "rgba(0,0,0,0.7)",
          color: "white",
          borderRadius: "8px",
          position: "fixed",
          bottom: "20px",
          right: "20px",
          zIndex: 1000,
          maxWidth: "300px"
        }}>
          <h3 style={{ margin: "0 0 10px 0" }}>Video Call Error</h3>
          <p>There was an error with the video call component. Please try again later.</p>
          <button
            onClick={() => this.setState({ hasError: false, error: null })}
            style={{
              padding: "8px 16px",
              backgroundColor: "#0078d4",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer"
            }}
          >
            Retry
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Create the video call context
export const VideoCallContext = createContext();

/**
 * Custom hook to use the video call context
 */
export const useVideoCallContext = () => {
  const context = useContext(VideoCallContext);
  if (!context) {
    throw new Error("useVideoCallContext must be used within a VideoCallProvider");
  }
  return context;
};

/**
 * VideoCallProvider component to manage video call state and UI
 */
const VideoCallProvider = ({ children }) => {
  const { user, tokens } = useAuth();

  // Initialize chat hook for call activity tracking
  const {
    connectionStatus,
    sendCallActivityMessage
  } = useChat(user?.agoraid?.toString(), tokens?.chatToken);

    let rtcToken = null;

  if (user?.agoraid === "i43w25b73m") {
    // Fallback token for testing
    rtcToken = "007eJxSYFC+fvqh3tKOReEZLtoRN5c8sDy1dsWblsyd0hyLXeY+5bivwGBiaW6QlJxmYWyUkmaSmJyWaJloaGmYmmSanJSWnGKeMiM+MuOAOifDbO9kBkYGRgYWBkYGEJ8JTDKDSRYwycDAxZBpYlxuZJpkbpwLCAAA//8rUyW5";
  }
  else if (user?.agoraid === "rc03of2eme") {
    // Fallback token for testing
    rtcToken = "007eJxSYJj1INfg4vePz7UE0x/rX/hyRsP/N9NGzpkaq5IjDnB3Ly5XYDCxNDdISk6zMDZKSTNJTE5LtEw0tDRMTTJNTkpLTjFPeR0fmXFAnZNB2suRkZGBkYGFgZEBxGcCk8xgkgVMMjBwMRQlGxjnpxml5qYCAgAA//8mAiXV";
  }

  // Initialize video call hook
  const videoCall = useVideoCall(
    user?.agoraid?.toString(),
    tokens?.rtmToken,
    rtcToken,
    user
  );

  // Destructure video call properties and methods
  const {
    callStatus,
    error,
    activeCall,
    incomingCall,
    localVideoTrack,
    localAudioTrack,
    remoteUsers,
    isVideoEnabled,
    isAudioEnabled,
    callDuration,
    callHistory,
    formatCallDuration,
    startCall,
    answerCall,
    rejectCall,
    endCall,
    cancelCall,
    toggleVideo,
    toggleAudio,
    clearError,
    setChatService,
  } = videoCall;

  // Connect video call service to chat service for call activity tracking
  useEffect(() => {
    if (connectionStatus === "connected" && typeof sendCallActivityMessage === "function") {
      const chatService = {
        sendCallActivityMessage
      };
      setChatService(chatService);
      console.log("Connected video call service to chat for call activity tracking");
    }
  }, [connectionStatus, sendCallActivityMessage, setChatService]);

  // Update video call when RTC token changes
  useEffect(() => {
    if (tokens && tokens.rtcToken && activeCall) {
      console.log("RTC token updated during active call - refreshing connection");
      // The useVideoCall hook will handle the token update automatically
      // when the rtcToken dependency changes
    }
  }, [tokens, activeCall]);

  // Create context value
  const contextValue = {
    callStatus,
    activeCall,
    incomingCall,
    localVideoTrack,
    localAudioTrack,
    remoteUsers,
    isVideoEnabled,
    isAudioEnabled,
    callDuration,
    formatCallDuration,
    startCall,
    answerCall,
    rejectCall,
    endCall,
    cancelCall, // Add the cancelCall function to the context value
    toggleVideo,
    toggleAudio,
    clearError,
    setChatService, // Add the setChatService function to the context value
  };

  return (
    <VideoCallContext.Provider value={contextValue}>
      {children}

      {/* Render video call UI components with error boundary */}
      <VideoCallErrorBoundary>
        <VideoCallComponent
          activeCall={activeCall}
          callStatus={callStatus}
          callDuration={callDuration}
          localVideoTrack={localVideoTrack}
          localAudioTrack={localAudioTrack}
          remoteUsers={remoteUsers}
          isVideoEnabled={isVideoEnabled}
          isAudioEnabled={isAudioEnabled}
          onEndCall={endCall}
          onToggleVideo={toggleVideo}
          onToggleAudio={toggleAudio}
          formatCallDuration={formatCallDuration}
        />

        <VideoIncomingCallModal
          incomingCall={incomingCall}
          onAnswer={answerCall}
          onReject={rejectCall}
        />
      </VideoCallErrorBoundary>
    </VideoCallContext.Provider>
  );
};

export default VideoCallProvider;
